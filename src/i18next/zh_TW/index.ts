import { useLogin, useVfy2Fa } from './api';
import {
  dateRange,
  tbApFilterDropdown,
  tagStatusMcOrder,
  tagTransactionType,
  tagTxStatus,
  tagTxType,
  bindingGA,
  mainAccountVerify,
  searchMaster,
} from './components';
import { useMerchantHub } from './hooks';
import { balanceHeader, notifyItem, privateHeader, privateLayout } from './layouts';
import {
  loginVerify,
  login,
  depositCreate,
  depositVerify,
  deposit,
  useDepositMerchantColumns,
  diOrder,
  useDiOrderColumns,
  ipCreate,
  ipDelete,
  ipList,
  ipUpdate,
  useIpListColumns,
  ipOverview,
  balanceCard,
  overview,
  accountJournal,
  useAccountJournalColumns,
  useSummaryAccountJournal,
  record,
  subAccountCreate,
  subAccountDetail,
  subAccount,
  useSubAccountColumns,
  changeOPW,
  changePW,
  setOpwModal,
  user,
  paymentAddress,
  transferCreate,
  transferVerify,
  useWalletMerchantColumns,
  wallet,
  withdrawDetail,
  withdrawVerify,
  useWithdrawMerchantColumns,
  withdraw,
  orderDetails,
  useOrderSummary,
} from './pages';
import { options } from './utils';

export default {
  useLogin,
  useVfy2Fa,

  dateRange,
  tbApFilterDropdown,
  tagStatusMcOrder,
  tagTransactionType,
  tagTxStatus,
  tagTxType,
  bindingGA,
  mainAccountVerify,
  searchMaster,

  useMerchantHub,

  balanceHeader,
  notifyItem,
  privateHeader,
  privateLayout,

  loginVerify,
  login,

  depositCreate,
  depositVerify,
  deposit,
  useDepositMerchantColumns,

  diOrder,
  useDiOrderColumns,

  ipCreate,
  ipDelete,
  ipList,
  ipUpdate,
  useIpListColumns,
  ipOverview,

  balanceCard,
  overview,

  accountJournal,
  useAccountJournalColumns,
  useSummaryAccountJournal,
  record,

  subAccountCreate,
  subAccountDetail,
  subAccount,
  useSubAccountColumns,

  changeOPW,
  changePW,
  setOpwModal,
  user,

  paymentAddress,
  transferCreate,
  transferVerify,
  useWalletMerchantColumns,
  wallet,

  withdrawDetail,
  withdrawVerify,
  useWithdrawMerchantColumns,
  withdraw,

  orderDetails,
  useOrderSummary,

  options,
};
