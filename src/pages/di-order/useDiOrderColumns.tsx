// libs
import { useCallback, useMemo, useState } from 'react';
import dayjs from 'dayjs';
import { Avatar, Space, TableColumnsType, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';

// apis
import { DiOrderOptions, DiOrderListRes } from '@/api';

// assets
import usdt from '@/assets/usdt.png';

// components
import { Txt, TxtCompressible } from '@/components/TypographyMaster';
import { TagTxStatus, TagTxType } from '@/components/TagAlpha';

// hooks
import { dateFormator } from '@/hook';

// utils
import { CryptoEnum, cryptoEnumOptions, nTot, valuesToFilter, valueToLabel } from '@/utils';

// store
import { useThemeStore } from '@/store/useThemeStore';

type DiOrderColumnsProps = {
  data: DiOrderListRes | undefined;
  setDiOrderMatchFrom: React.Dispatch<React.SetStateAction<DiOrderOptions | undefined>>;
};

const useDiOrderColumns = (useProps: DiOrderColumnsProps) => {
  // props
  const { data, setDiOrderMatchFrom } = useProps;

  // states
  const [hoveredKeys, setHoveredKeys] = useState<Array<string>>([]);
  const [expandedKeys, setExpandedKeys] = useState<Array<string>>([]);

  // hooks
  const { isWhite } = useThemeStore();
  const { t } = useTranslation('useDiOrderColumns');

  // compute
  const onClickStatus = useCallback(
    (row: DiOrderOptions) => {
      setDiOrderMatchFrom(data?.items.find((findI) => findI.orderUid === row.orderUid));
    },
    [setDiOrderMatchFrom, data?.items],
  );

  const columns = useMemo(() => {
    const result: TableColumnsType<DiOrderOptions> = [
      {
        key: 'orderUid',
        dataIndex: 'orderUid',
        title: <Txt>{t('orderUid')}</Txt>,
        align: 'center',
        render: (_, { orderUid }) => (
          <TxtCompressible
            text={orderUid}
            maxLength={12}
          />
        ),
      },
      {
        key: 'merchantOrderId',
        dataIndex: 'merchantOrderId',
        title: <Txt>{t('merchantOrderId')}</Txt>,
        align: 'center',
        render: (_, { merchantOrderId }) => (
          <TxtCompressible
            text={merchantOrderId}
            maxLength={12}
          />
        ),
      },
      {
        key: 'memberId',
        dataIndex: 'memberId',
        title: <Txt>{t('memberId')}</Txt>,
        align: 'center',
        render: (_, { memberId }) => (
          <TxtCompressible
            text={memberId}
            maxLength={12}
          />
        ),
      },
      {
        key: 'payerBankAccountName',
        dataIndex: 'payerBankAccountName',
        title: <Txt>{t('payerBankAccountName')}</Txt>,
        align: 'center',
        render: (_, { payerBankAccountName }) => (
          <Txt>{payerBankAccountName}</Txt>
        ),
      },
      {
        key: 'entryCode',
        dataIndex: 'entryCode',
        title: <Txt>{t('entryCode')}</Txt>,
        align: 'center',
        render: (_, { entryCode }) => (
          <TxtCompressible
            text={entryCode}
            maxLength={12}
          />
        ),
      },
      {
        title: <Txt>{t('transactionType')}</Txt>,
        key: 'transactionType',
        align: 'center',
        render: ({ transactionType }) => {
          return <TagTxType transactionType={transactionType} />;
        },
      },
      {
        title: <Txt>{t('cryptoInfo')}</Txt>,
        key: 'cryptoInfo',
        align: 'center',
        render: (_, { cryptoType, cryptoAmount }) => {
          const cryptoOption = cryptoEnumOptions.find((option) => option.value === cryptoType);
          return (
            <main className='flex flex-col items-center'>
              <Space>
                <Avatar
                  size='small'
                  src={usdt}
                />
                <Txt>{cryptoOption?.label || cryptoType}</Txt>
              </Space>
              <Txt type='secondary'>{nTot(cryptoAmount)}</Txt>
            </main>
          );
        },
      },
      {
        title: <Txt>{t('fiatInfo')}</Txt>,
        key: 'fiatInfo',
        align: 'center',
        render: (_, { fiatType, fiatAmount }) => (
          <main className='flex flex-col items-center'>
            <Txt>{fiatType}</Txt>
            <Txt type='secondary'>{nTot(fiatAmount)}</Txt>
          </main>
        ),
      },
      {
        title: <Txt>{t('createdAt')}</Txt>,
        key: 'createdAt',
        align: 'center',
        render: (_, { createdAt }) => {
          const formattedDate = dateFormator(createdAt);
          return (
            <main className='flex flex-col items-center'>
              <Txt>{formattedDate.date}</Txt>
              <Txt type='secondary'>{formattedDate.time}</Txt>
            </main>
          );
        },
      },
      {
        title: <Txt>{t('merchantNotified')}</Txt>,
        key: 'isMerchantNotified',
        align: 'center',
        render: (_, { isMerchantNotified }) => (
          <Txt type={isMerchantNotified ? 'success' : 'danger'}>
            {isMerchantNotified ? t('notified') : t('notNotified')}
          </Txt>
        ),
      },
      {
        title: <Txt>{t('status')}</Txt>,
        key: 'status',
        align: 'center',
        render: (_, item) => {
          return (
            <TagTxStatus
              status={item.status}
              tooltip={t('statusTooltip')}
              onClick={() => onClickStatus(item)}
            />
          );
        },
      },
    ];

    return result;
  }, [data?.items, expandedKeys, hoveredKeys, isWhite, onClickStatus, t]);

  return { columns };
};

export default useDiOrderColumns;
