import * as React from 'react';
import { useCallback, useMemo, useState } from 'react';
import { <PERSON>readcrumb, Flex, Select } from 'antd';
import dayjs from 'dayjs';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { DiOrderOptions, useDiOrderList } from '@/api';
import SearchMaster from '@/components/SearchMaster';
import TableAlpha from '@/components/TableAlpha';
import DateRange, { DateRangeOptions } from '@/components/DateRange';
import { dateFormator, useTableStates } from '@/hook';
import {
  arrayObjToArraySheet,
  exportSheetByArray,
  storageHelper,
  TxStatusNum,
  txStatusOptions,
} from '@/utils';
import { Txt } from '@/components/TypographyMaster';
import OrderDetails from '../OrderDetails';
import useDiOrderColumns from './useDiOrderColumns';

interface IDiOrderProps {
  isActive?: boolean;
}

const DiOrder: React.FunctionComponent<IDiOrderProps> = (props) => {
  // props
  const {} = props || {};

  const storageRange = storageHelper<{ from: string; to: string }>('diOrderRange').getItem();
  const { t } = useTranslation('diOrder');
  const { t: optionsT } = useTranslation('options');
  const [diOrderMatchFrom, setDiOrderMatchFrom] = useState<DiOrderOptions>();
  const { pageSize, setPageSize, currentPage, setCurrentPage } = useTableStates({});
  const defaultDateRange = storageRange
    ? { from: dayjs(storageRange.from), to: dayjs(storageRange.to) }
    : { from: dayjs().startOf('d'), to: dayjs().endOf('d') };
  const [OrderUid, setOrderUid] = useState<string>();
  const [dateRange, setDateRange] = useState<DateRangeOptions>(defaultDateRange);
  const [MerchantOrderId, setMerchantOrderId] = useState<string>();
  const [Status, setStatus] = useState<Array<TxStatusNum>>();

  const translateTxStatusOptions = useMemo(() => {
    return txStatusOptions.map((option) => ({
      ...option,
      label: optionsT(option.label),
    }));
  }, [optionsT]);

  const { data, isPending, isRefetching } = useDiOrderList({
    params: {
      PageNumber: currentPage,
      PageSize: pageSize,
      OrderByDescending: true,
      OrderUid,
      MerchantOrderId,
      CreatedAtStart: dateFormator(dateRange.from).dateTime,
      CreatedAtEnd: dateFormator(dateRange.to).dateTime,
      Status,
    },
  });

  const dataSource = useMemo(() => {
    return Array.isArray(data?.items) ? data.items : [];
  }, [data]);

  const { columns } = useDiOrderColumns({ data, setDiOrderMatchFrom });

  const handleOnDateSubmit = useCallback((newDate: DateRangeOptions) => {
    setDateRange(newDate);
    storageHelper<DateRangeOptions>('diOrderRange').setItem(newDate);
  }, []);

  return (
    <div>
      <Breadcrumb
        items={[
          {
            title: <Link to='private'>{t('breadcrumbHome')}</Link>,
          },
          {
            title: t('breadcrumbCurrent'),
          },
        ]}
      />
      <Flex className='my-5 flex flex-wrap justify-between gap-2'>
        <Flex gap={10}>
          {/* Future: Add any action buttons here */}
        </Flex>
        <Flex
          gap={10}
          className='items-center'
        >
          <DateRange
            loading={isRefetching}
            onDateSubmit={handleOnDateSubmit}
            defaultValues={dateRange}
          />
        </Flex>
      </Flex>
      <TableAlpha
        {...{ dataSource, columns, pageSize, setPageSize, currentPage, setCurrentPage }}
        loading={isPending}
        totalDataLength={data?.totalCount}
        size='small'
        rowKey='orderUid'
        titleRender={
          <div className='items- flex flex-wrap items-center gap-x-2'>
            <div className='flex flex-col gap-y-1'>
              <Txt
                type='secondary'
                className='font-bold'
              >
                {t('statusLabel')}:
              </Txt>
              <Select
                placeholder={t('statusPlaceholder')}
                variant='filled'
                className='w-[160px]'
                options={translateTxStatusOptions}
                disabled={isRefetching}
                onChange={(newValue) => setStatus(newValue)}
                allowClear
                mode='multiple'
              />
            </div>
            <SearchMaster
              titles={[
                { key: 'OrderUid', label: t('orderUidLabel') },
                { key: 'MerchantOrderId', label: t('merchantOrderIdLabel') },
              ]}
              onSearch={(values) => {
                setOrderUid(values.OrderUid || undefined);
                setMerchantOrderId(values.MerchantOrderId || undefined);
              }}
            />
          </div>
        }
      />
      <OrderDetails
        order={diOrderMatchFrom}
        setOrder={setDiOrderMatchFrom}
      />
    </div>
  );
};

export default DiOrder;
