// hooks
import { useTestQuery, UseTestQueryProps } from '@/hook';

// utils
import { axiosRoot } from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';
import { CryptoEnum, TxStatusNum } from '@/utils';
import dayjs from "dayjs";

type DiOrderOptions = {
  orderUid: string;
  merchantOrderId: string;
  memberId: string;
  payerBankAccountName: string;
  entryCode: string;
  transactionType: number;
  cryptoType: CryptoEnum;
  cryptoAmount: number;
  fiatType: string;
  fiatAmount: number;
  status: TxStatusNum;
  createdAt: string;
  isMerchantNotified: boolean;
};

type DiOrderListRes = {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: DiOrderOptions[];
};

type DiOrderListProps = {
  PageNumber?: number;
  PageSize?: number;
  OrderBy?: string;
  OrderByDescending?: boolean;
  OrderUid?: string;
  MerchantOrderId?: string;
  CreatedAtStart?: dayjs.Dayjs;
  CreatedAtEnd?: dayjs.Dayjs;
  Status?: Array<TxStatusNum>;
  TransactionType?: number;
  CryptoType?: CryptoEnum;
};

type Other = {};
const useDiOrderList = (useProps: UseTestQueryProps<Other, DiOrderListProps>) => {
  // props
  const { params, onSuccess, ...config } = useProps;

  const testQuery = useTestQuery<DiOrderListRes, DiOrderListProps>({
    ...config,
    queryKey: queryKeys.query.diOrderList(params),
    qf: () => {
      const request = axiosRoot.get('/V1.0/order/payment-di/me', { params }).then(({ data }) => data);

      return request;
    },
    onSuccess: (res) => {
      if (onSuccess) onSuccess(res, params);
    },
    staleTime: 1000 * 5,
  });

  return testQuery;
};

export { useDiOrderList };

export type { DiOrderOptions, DiOrderListRes, DiOrderListProps };
